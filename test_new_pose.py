#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的垂直向下姿态参数
验证坐标转换是否正确
"""

import math

# 新的垂直向下姿态 (基于API读取的弧度值)
DEFAULT_VERTICAL_POSE = {
    'rx': 3.141503,   # A轴 (≈179.994°)
    'ry': -0.009545,  # B轴 (≈-0.547°)
    'rz': 0.000000    # C轴 (0°)
}

# 新的工作台原点位置 (基于示教盒当前位置)
WORKBENCH_ORIGIN_ROBOT = [0.661651, -0.0042, 1.078137]  # (m)
GCODE_SCALE = 0.001

def gcode_to_robot_coordinates_test(gcode_point):
    """测试坐标转换函数"""
    # 缩放G-code坐标 (mm -> m)
    x_scaled = gcode_point['x'] * GCODE_SCALE
    y_scaled = gcode_point['y'] * GCODE_SCALE  
    z_scaled = gcode_point['z'] * GCODE_SCALE
    
    # 添加工作台原点偏移
    x_robot = WORKBENCH_ORIGIN_ROBOT[0] + x_scaled
    y_robot = WORKBENCH_ORIGIN_ROBOT[1] + y_scaled
    z_robot = WORKBENCH_ORIGIN_ROBOT[2] + z_scaled
    
    # 使用新的垂直姿态
    rx_robot = DEFAULT_VERTICAL_POSE['rx']
    ry_robot = DEFAULT_VERTICAL_POSE['ry']
    rz_robot = DEFAULT_VERTICAL_POSE['rz']
    
    return {
        'x': x_robot,
        'y': y_robot, 
        'z': z_robot,
        'rx': rx_robot,
        'ry': ry_robot,
        'rz': rz_robot
    }

def main():
    print("=" * 60)
    print("测试新的垂直向下姿态参数")
    print("=" * 60)
    
    print(f"📍 工作台原点位置: {WORKBENCH_ORIGIN_ROBOT}")
    print(f"🔧 垂直向下姿态: {DEFAULT_VERTICAL_POSE}")
    print(f"📏 坐标缩放因子: {GCODE_SCALE}")
    
    # 测试几个G-code点
    test_points = [
        {'x': 0.0, 'y': 0.0, 'z': 0.0},      # 原点
        {'x': 10.0, 'y': 10.0, 'z': 5.0},    # 小偏移
        {'x': 100.0, 'y': 50.0, 'z': 20.0},  # 较大偏移
    ]
    
    print(f"\n🔍 坐标转换测试:")
    print("-" * 90)
    print(f"{'G-code (mm)':<20} {'Robot Position (m)':<30} {'Robot Orientation (rad)':<25} {'Orientation (deg)':<15}")
    print("-" * 90)
    
    for i, gcode_point in enumerate(test_points):
        robot_point = gcode_to_robot_coordinates_test(gcode_point)
        
        gcode_str = f"({gcode_point['x']:.1f}, {gcode_point['y']:.1f}, {gcode_point['z']:.1f})"
        robot_pos_str = f"({robot_point['x']:.6f}, {robot_point['y']:.6f}, {robot_point['z']:.6f})"
        robot_ori_str = f"({robot_point['rx']:.3f}, {robot_point['ry']:.3f}, {robot_point['rz']:.3f})"
        
        print(f"{gcode_str:<20} {robot_pos_str:<30} {robot_ori_str:<25}")
    
    print("-" * 80)
    print("\n✅ 现在G-code原点(0,0,0)对应的机器人位置就是你当前的垂直向下位置!")
    print("✅ 所有没有ABC角度的G-code点都会使用正确的垂直向下姿态!")

if __name__ == "__main__":
    main()
